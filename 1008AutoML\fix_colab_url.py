# Run this in a NEW CELL in your Colab notebook to fix the URL

from pyngrok import ngrok

# Get active tunnels
tunnels = ngrok.get_tunnels()
print(f"Found {len(tunnels)} active tunnels")

if tunnels:
    # Get the first tunnel (should be our Ollama tunnel)
    tunnel = tunnels[0]
    public_url = str(tunnel.public_url)
    
    print(f"🚀 Found tunnel: {public_url}")
    
    # Save the URL to file
    with open('/content/ollama_url.txt', 'w') as f:
        f.write(public_url)
    
    print(f"✅ URL saved to file")
    
    # Display connection info
    print("\n" + "="*60)
    print("🚀 OLLAMA SERVER IS RUNNING!")
    print("="*60)
    print(f"Public URL: {public_url}")
    print(f"\nTo connect from your local machine:")
    print(f"export OLLAMA_HOST={public_url}")
    print(f"\nOr in Python:")
    print(f"import os")
    print(f"os.environ['OLLAMA_HOST'] = '{public_url}'")
    
    # Test the connection
    import requests
    try:
        response = requests.get(f"{public_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"\nAvailable models:")
            for model in models:
                print(f"  - {model['name']}")
        else:
            print(f"\n❌ API test failed: {response.status_code}")
    except Exception as e:
        print(f"\n❌ API test error: {e}")
    
else:
    print("❌ No active tunnels found!")
    print("You may need to restart the ngrok tunnel.")
    
    # Try to create a new tunnel
    try:
        print("Attempting to create new tunnel...")
        tunnel = ngrok.connect(11434, "http")
        public_url = str(tunnel.public_url)
        
        with open('/content/ollama_url.txt', 'w') as f:
            f.write(public_url)
            
        print(f"✅ New tunnel created: {public_url}")
    except Exception as e:
        print(f"❌ Failed to create tunnel: {e}")
