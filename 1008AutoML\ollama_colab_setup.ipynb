{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ollama on Google Colab with <PERSON>rok Tunnel\n", "\n", "This notebook sets up Ollama on Google Colab and exposes it via ngrok tunnel for local access."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Mount Google Drive (Optional - for model persistence)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "import os\n", "\n", "# Mount Google Drive\n", "drive.mount('/content/drive')\n", "\n", "# Create ollama models directory in Google Drive\n", "ollama_models_dir = '/content/drive/MyDrive/ollama_models'\n", "os.makedirs(ollama_models_dir, exist_ok=True)\n", "\n", "print(f\"Ollama models will be stored in: {ollama_models_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download and install ollama\n", "!curl -fsSL https://ollama.ai/install.sh | sh\n", "\n", "# Install required Python packages\n", "!pip install aiohttp pyngrok requests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Setup Environment and Ngrok"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import asyncio\n", "import time\n", "from pyngrok import ngrok\n", "\n", "# Set LD_LIBRARY_PATH for NVIDIA libraries\n", "os.environ.update({'LD_LIBRARY_PATH': '/usr/lib64-nvidia'})\n", "\n", "# Set OLLAMA_MODELS to Google Drive directory (optional)\n", "os.environ['OLLAMA_MODELS'] = '/content/drive/MyDrive/ollama_models'\n", "\n", "# Your ngrok auth token\n", "NGROK_AUTH_TOKEN = \"30ok0ovqvAQ2PwN7u3iF72gWlvU_7xfnRpU7FmDqZabUhP9zg\"\n", "\n", "# Set ngrok auth token\n", "ngrok.set_auth_token(NGROK_AUTH_TOKEN)\n", "\n", "print(\"Environment setup complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: <PERSON> O<PERSON>ma Server and <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import threading\n", "import time\n", "from pyngrok import ngrok\n", "\n", "def start_ollama_server():\n", "    \"\"\"Start Ollama server in background\"\"\"\n", "    try:\n", "        subprocess.run(['ollama', 'serve'], check=True)\n", "    except Exception as e:\n", "        print(f\"Error starting Ollama server: {e}\")\n", "\n", "# Start Ollama server in a separate thread\n", "server_thread = threading.Thread(target=start_ollama_server, daemon=True)\n", "server_thread.start()\n", "\n", "# Wait a bit for server to start\n", "print(\"Starting Ollama server...\")\n", "time.sleep(5)\n", "\n", "# Create ngrok tunnel\n", "try:\n", "    # Kill any existing tunnels\n", "    ngrok.kill()\n", "    \n", "    # Create new tunnel\n", "    tunnel = ngrok.connect(11434, \"http\")\n", "    public_url = str(tunnel.public_url)\n", "    print(f\"\\n🚀 Ollama server is now accessible at: {public_url}\")\n", "    print(f\"\\n📋 Copy this URL and use it as OLLAMA_HOST on your local machine:\")\n", "    print(f\"export OLLAMA_HOST={public_url}\")\n", "    print(f\"\\nOr in Python: os.environ['OLLAMA_HOST'] = '{public_url}'\")\n", "    \n", "    # Store the URL for later use\n", "    with open('/content/ollama_url.txt', 'w') as f:\n", "        f.write(public_url)\n", "        \n", "except Exception as e:\n", "    print(f\"Error creating ngrok tunnel: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Download and Test a Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import time\n", "\n", "# Wait for server to be ready\n", "time.sleep(10)\n", "\n", "# Test if <PERSON><PERSON><PERSON> is running\n", "try:\n", "    response = requests.get('http://localhost:11434/api/tags')\n", "    if response.status_code == 200:\n", "        print(\"✅ Ollama server is running!\")\n", "        print(\"Current models:\", response.json())\n", "    else:\n", "        print(\"❌ Ollama server not responding\")\n", "except Exception as e:\n", "    print(f\"❌ Error connecting to Ollama: {e}\")\n", "\n", "# Download a small model for testing (optional)\n", "model_name = \"llama3.2:1b\"  # Small model for testing\n", "print(f\"\\nDownloading {model_name} model...\")\n", "print(\"This may take a few minutes...\")\n", "\n", "try:\n", "    # Pull the model\n", "    pull_response = requests.post('http://localhost:11434/api/pull', \n", "                                 json={'name': model_name}, \n", "                                 stream=True)\n", "    \n", "    for line in pull_response.iter_lines():\n", "        if line:\n", "            data = json.loads(line)\n", "            if 'status' in data:\n", "                print(f\"Status: {data['status']}\")\n", "            if data.get('status') == 'success':\n", "                print(f\"✅ Model {model_name} downloaded successfully!\")\n", "                break\n", "                \n", "except Exception as e:\n", "    print(f\"Error downloading model: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Test the Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the model with a simple prompt\n", "def test_model(model_name, prompt):\n", "    try:\n", "        response = requests.post('http://localhost:11434/api/generate',\n", "                               json={\n", "                                   'model': model_name,\n", "                                   'prompt': prompt,\n", "                                   'stream': False\n", "                               })\n", "        \n", "        if response.status_code == 200:\n", "            result = response.json()\n", "            return result.get('response', 'No response')\n", "        else:\n", "            return f\"Error: {response.status_code}\"\n", "    except Exception as e:\n", "        return f\"Error: {e}\"\n", "\n", "# Test the model\n", "test_prompt = \"Hello! Can you tell me a short joke?\"\n", "print(f\"Testing model with prompt: '{test_prompt}'\")\n", "print(\"\\nResponse:\")\n", "response = test_model(model_name, test_prompt)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Keep the Server Running"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display connection info and keep running\n", "try:\n", "    with open('/content/ollama_url.txt', 'r') as f:\n", "        public_url = f.read().strip()\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🚀 OLLAMA SERVER IS RUNNING!\")\n", "    print(\"=\"*60)\n", "    print(f\"Public URL: {public_url}\")\n", "    print(f\"\\nTo connect from your local machine:\")\n", "    print(f\"export OLLAMA_HOST={public_url}\")\n", "    print(f\"\\nOr in Python:\")\n", "    print(f\"import os\")\n", "    print(f\"os.environ['OLLAMA_HOST'] = '{public_url}'\")\n", "    print(\"\\nAvailable models:\")\n", "    \n", "    # List available models\n", "    response = requests.get('http://localhost:11434/api/tags')\n", "    if response.status_code == 200:\n", "        models = response.json().get('models', [])\n", "        for model in models:\n", "            print(f\"  - {model['name']}\")\n", "    \n", "    print(\"\\n⚠️  Keep this cell running to maintain the connection!\")\n", "    print(\"=\"*60)\n", "    \n", "    # Keep the cell running\n", "    import time\n", "    while True:\n", "        time.sleep(60)\n", "        print(f\"Server still running... {time.strftime('%H:%M:%S')}\")\n", "        \n", "except KeyboardInterrupt:\n", "    print(\"\\nShutting down...\")\n", "    ngrok.kill()\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}