from google.colab import drive
import os

# Mount Google Drive
drive.mount('/content/drive')

# Create ollama models directory in Google Drive
ollama_models_dir = '/content/drive/MyDrive/ollama_models'
os.makedirs(ollama_models_dir, exist_ok=True)

print(f"Ollama models will be stored in: {ollama_models_dir}")

# Download and install ollama
!curl -fsSL https://ollama.ai/install.sh | sh

# Install required Python packages
!pip install aiohttp pyngrok requests

import os
import asyncio
import time
from pyngrok import ngrok

# Set LD_LIBRARY_PATH for NVIDIA libraries
os.environ.update({'LD_LIBRARY_PATH': '/usr/lib64-nvidia'})

# Set OLLAMA_MODELS to Google Drive directory (optional)
os.environ['OLLAMA_MODELS'] = '/content/drive/MyDrive/ollama_models'

# Your ngrok auth token
NGROK_AUTH_TOKEN = "30ok0ovqvAQ2PwN7u3iF72gWlvU_7xfnRpU7FmDqZabUhP9zg"

# Set ngrok auth token
ngrok.set_auth_token(NGROK_AUTH_TOKEN)

print("Environment setup complete!")

import subprocess
import threading
import time
from pyngrok import ngrok

def start_ollama_server():
    """Start Ollama server in background"""
    try:
        subprocess.run(['ollama', 'serve'], check=True)
    except Exception as e:
        print(f"Error starting Ollama server: {e}")

# Start Ollama server in a separate thread
server_thread = threading.Thread(target=start_ollama_server, daemon=True)
server_thread.start()

# Wait a bit for server to start
print("Starting Ollama server...")
time.sleep(5)

# Create ngrok tunnel
try:
    # Kill any existing tunnels
    ngrok.kill()
    
    # Create new tunnel
    public_url = ngrok.connect(11434, "http")
    print(f"\n🚀 Ollama server is now accessible at: {public_url}")
    print(f"\n📋 Copy this URL and use it as OLLAMA_HOST on your local machine:")
    print(f"export OLLAMA_HOST={public_url}")
    print(f"\nOr in Python: os.environ['OLLAMA_HOST'] = '{public_url}'")
    
    # Store the URL for later use
    with open('/content/ollama_url.txt', 'w') as f:
        f.write(public_url)
        
except Exception as e:
    print(f"Error creating ngrok tunnel: {e}")

import requests
import json
import time

# Wait for server to be ready
time.sleep(10)

# Test if Ollama is running
try:
    response = requests.get('http://localhost:11434/api/tags')
    if response.status_code == 200:
        print("✅ Ollama server is running!")
        print("Current models:", response.json())
    else:
        print("❌ Ollama server not responding")
except Exception as e:
    print(f"❌ Error connecting to Ollama: {e}")

# Download a small model for testing (optional)
model_name = "llama3.2:1b"  # Small model for testing
print(f"\nDownloading {model_name} model...")
print("This may take a few minutes...")

try:
    # Pull the model
    pull_response = requests.post('http://localhost:11434/api/pull', 
                                 json={'name': model_name}, 
                                 stream=True)
    
    for line in pull_response.iter_lines():
        if line:
            data = json.loads(line)
            if 'status' in data:
                print(f"Status: {data['status']}")
            if data.get('status') == 'success':
                print(f"✅ Model {model_name} downloaded successfully!")
                break
                
except Exception as e:
    print(f"Error downloading model: {e}")

# Test the model with a simple prompt
def test_model(model_name, prompt):
    try:
        response = requests.post('http://localhost:11434/api/generate',
                               json={
                                   'model': model_name,
                                   'prompt': prompt,
                                   'stream': False
                               })
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', 'No response')
        else:
            return f"Error: {response.status_code}"
    except Exception as e:
        return f"Error: {e}"

# Test the model
test_prompt = "Hello! Can you tell me a short joke?"
print(f"Testing model with prompt: '{test_prompt}'")
print("\nResponse:")
response = test_model(model_name, test_prompt)
print(response)

# Display connection info and keep running
try:
    with open('/content/ollama_url.txt', 'r') as f:
        public_url = f.read().strip()
    
    print("\n" + "="*60)
    print("🚀 OLLAMA SERVER IS RUNNING!")
    print("="*60)
    print(f"Public URL: {public_url}")
    print(f"\nTo connect from your local machine:")
    print(f"export OLLAMA_HOST={public_url}")
    print(f"\nOr in Python:")
    print(f"import os")
    print(f"os.environ['OLLAMA_HOST'] = '{public_url}'")
    print("\nAvailable models:")
    
    # List available models
    response = requests.get('http://localhost:11434/api/tags')
    if response.status_code == 200:
        models = response.json().get('models', [])
        for model in models:
            print(f"  - {model['name']}")
    
    print("\n⚠️  Keep this cell running to maintain the connection!")
    print("="*60)
    
    # Keep the cell running
    import time
    while True:
        time.sleep(60)
        print(f"Server still running... {time.strftime('%H:%M:%S')}")
        
except KeyboardInterrupt:
    print("\nShutting down...")
    ngrok.kill()
except Exception as e:
    print(f"Error: {e}")