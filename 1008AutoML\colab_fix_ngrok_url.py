# Quick fix for the ngrok URL issue in Colab
# Run this cell in your Colab notebook to get the correct URL

from pyngrok import ngrok

# Get the current tunnel
tunnels = ngrok.get_tunnels()
if tunnels:
    public_url = str(tunnels[0].public_url)
    print(f"🚀 Correct Ollama URL: {public_url}")
    print(f"\n📋 Use this on your local machine:")
    print(f"export OLLAMA_HOST={public_url}")
    print(f"\nOr in Python:")
    print(f"os.environ['OLLAMA_HOST'] = '{public_url}'")
    
    # Save the correct URL
    with open('/content/ollama_url.txt', 'w') as f:
        f.write(public_url)
    
    print(f"\n✅ URL saved to /content/ollama_url.txt")
else:
    print("❌ No active ngrok tunnels found")
    print("The tunnel might have been closed. Try restarting the ngrok setup.")
