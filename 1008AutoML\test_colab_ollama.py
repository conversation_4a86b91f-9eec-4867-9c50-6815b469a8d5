#!/usr/bin/env python3
"""
Quick test script for Colab Ollama connection
"""

import os
import requests
import sys

def test_ollama_connection(ngrok_url):
    """Quick test of Ollama connection"""
    
    # Set environment variable
    os.environ['OLLAMA_HOST'] = ngrok_url
    
    print(f"🔗 Testing connection to: {ngrok_url}")
    
    try:
        # Test basic connection
        response = requests.get(f"{ngrok_url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            print("✅ Connection successful!")
            
            # List models
            models = response.json().get('models', [])
            if models:
                print(f"📋 Found {len(models)} model(s):")
                for model in models:
                    print(f"  - {model['name']}")
                
                # Test a simple generation
                model_name = models[0]['name']
                print(f"\n🤖 Testing generation with {model_name}...")
                
                gen_response = requests.post(f"{ngrok_url}/api/generate",
                                           json={
                                               'model': model_name,
                                               'prompt': 'Say hello in one sentence.',
                                               'stream': False
                                           },
                                           timeout=30)
                
                if gen_response.status_code == 200:
                    result = gen_response.json()
                    print(f"Response: {result.get('response', 'No response')}")
                    print("\n✅ Everything is working!")
                else:
                    print(f"❌ Generation failed: {gen_response.status_code}")
            else:
                print("📭 No models found. You may need to download one first.")
                print("Example: ollama pull llama3.2:1b")
        else:
            print(f"❌ Connection failed: {response.status_code}")
            print("Make sure your Colab notebook is running and ngrok tunnel is active.")
            
    except requests.exceptions.Timeout:
        print("❌ Connection timeout. Check your ngrok URL and internet connection.")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error. Make sure the ngrok URL is correct and accessible.")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_colab_ollama.py <ngrok_url>")
        print("Example: python test_colab_ollama.py https://abc123.ngrok.io")
        sys.exit(1)
    
    ngrok_url = sys.argv[1].rstrip('/')
    test_ollama_connection(ngrok_url)

if __name__ == "__main__":
    main()
