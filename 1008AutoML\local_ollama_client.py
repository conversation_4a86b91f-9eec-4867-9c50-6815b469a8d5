#!/usr/bin/env python3
"""
Local Ollama Client for connecting to Colab-hosted Ollama via ngrok
"""

import os
import requests
import json
import time
from typing import Optional, Dict, Any, Iterator

class ColabOllamaClient:
    def __init__(self, ngrok_url: str):
        """
        Initialize the client with the ngrok URL from Colab
        
        Args:
            ngrok_url: The ngrok URL from your Colab notebook (e.g., https://abc123.ngrok.io)
        """
        self.base_url = ngrok_url.rstrip('/')
        self.api_url = f"{self.base_url}/api"
        
        # Set environment variable for ollama CLI compatibility
        os.environ['OLLAMA_HOST'] = ngrok_url
        
        print(f"🔗 Connected to Ollama at: {self.base_url}")
        
    def test_connection(self) -> bool:
        """Test if the connection to Ollama is working"""
        try:
            response = requests.get(f"{self.api_url}/tags", timeout=10)
            if response.status_code == 200:
                print("✅ Connection successful!")
                return True
            else:
                print(f"❌ Connection failed with status: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    def list_models(self) -> list:
        """List all available models"""
        try:
            response = requests.get(f"{self.api_url}/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                print("📋 Available models:")
                for model in models:
                    print(f"  - {model['name']} (Size: {model.get('size', 'Unknown')})")
                return models
            else:
                print(f"Error listing models: {response.status_code}")
                return []
        except Exception as e:
            print(f"Error listing models: {e}")
            return []
    
    def pull_model(self, model_name: str) -> bool:
        """Download a model to the Colab instance"""
        print(f"📥 Pulling model: {model_name}")
        print("This may take several minutes...")
        
        try:
            response = requests.post(f"{self.api_url}/pull", 
                                   json={'name': model_name}, 
                                   stream=True,
                                   timeout=300)
            
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line)
                        if 'status' in data:
                            print(f"Status: {data['status']}")
                            if 'completed' in data and 'total' in data:
                                progress = (data['completed'] / data['total']) * 100
                                print(f"Progress: {progress:.1f}%")
                        if data.get('status') == 'success':
                            print(f"✅ Model {model_name} downloaded successfully!")
                            return True
                    except json.JSONDecodeError:
                        continue
            return False
        except Exception as e:
            print(f"Error pulling model: {e}")
            return False
    
    def generate(self, model: str, prompt: str, stream: bool = False) -> str:
        """Generate text using the specified model"""
        try:
            payload = {
                'model': model,
                'prompt': prompt,
                'stream': stream
            }
            
            response = requests.post(f"{self.api_url}/generate", 
                                   json=payload,
                                   stream=stream)
            
            if stream:
                full_response = ""
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if 'response' in data:
                                chunk = data['response']
                                print(chunk, end='', flush=True)
                                full_response += chunk
                            if data.get('done', False):
                                print()  # New line at the end
                                break
                        except json.JSONDecodeError:
                            continue
                return full_response
            else:
                if response.status_code == 200:
                    result = response.json()
                    return result.get('response', 'No response')
                else:
                    return f"Error: {response.status_code} - {response.text}"
                    
        except Exception as e:
            return f"Error: {e}"
    
    def chat(self, model: str, messages: list, stream: bool = False) -> str:
        """Chat with the model using conversation format"""
        try:
            payload = {
                'model': model,
                'messages': messages,
                'stream': stream
            }
            
            response = requests.post(f"{self.api_url}/chat", 
                                   json=payload,
                                   stream=stream)
            
            if stream:
                full_response = ""
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if 'message' in data and 'content' in data['message']:
                                chunk = data['message']['content']
                                print(chunk, end='', flush=True)
                                full_response += chunk
                            if data.get('done', False):
                                print()  # New line at the end
                                break
                        except json.JSONDecodeError:
                            continue
                return full_response
            else:
                if response.status_code == 200:
                    result = response.json()
                    return result.get('message', {}).get('content', 'No response')
                else:
                    return f"Error: {response.status_code} - {response.text}"
                    
        except Exception as e:
            return f"Error: {e}"


def main():
    """Example usage of the ColabOllamaClient"""
    
    # Replace this with your actual ngrok URL from Colab
    NGROK_URL = "https://your-ngrok-url.ngrok.io"  # Update this!
    
    print("🚀 Colab Ollama Client")
    print("=" * 50)
    
    # Initialize client
    client = ColabOllamaClient(NGROK_URL)
    
    # Test connection
    if not client.test_connection():
        print("❌ Failed to connect. Make sure:")
        print("1. Your Colab notebook is running")
        print("2. The ngrok URL is correct")
        print("3. The Ollama server is started in Colab")
        return
    
    # List available models
    models = client.list_models()
    
    if not models:
        print("\n📥 No models found. Let's download one...")
        model_name = "llama3.2:1b"  # Small model for testing
        if client.pull_model(model_name):
            print(f"✅ Model {model_name} is ready!")
        else:
            print("❌ Failed to download model")
            return
    else:
        model_name = models[0]['name']
    
    # Test generation
    print(f"\n🤖 Testing generation with model: {model_name}")
    prompt = "Hello! Tell me a short joke about programming."
    print(f"Prompt: {prompt}")
    print("Response:", end=" ")
    
    response = client.generate(model_name, prompt, stream=True)
    
    # Test chat format
    print(f"\n💬 Testing chat format...")
    messages = [
        {"role": "user", "content": "What is the capital of France?"}
    ]
    print("Chat response:", end=" ")
    chat_response = client.chat(model_name, messages, stream=True)


if __name__ == "__main__":
    main()
