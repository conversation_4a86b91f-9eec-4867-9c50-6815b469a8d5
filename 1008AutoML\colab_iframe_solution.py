# SOLUTION 2 IMPROVED - Use this in your Colab notebook

import subprocess
import time
import requests
import json
from google.colab import output

def start_ollama_server():
    """Start Ollama server"""
    try:
        # Start Ollama server
        process = subprocess.Popen(['ollama', 'serve'])
        print("Ollama server started...")
        return process
    except Exception as e:
        print(f"Error starting Ollama server: {e}")
        return None

# Start the server
server_process = start_ollama_server()
time.sleep(10)  # Wait for server to start

# Test local connection first
print("Testing local connection...")
try:
    response = requests.get('http://localhost:11434/api/tags', timeout=5)
    if response.status_code == 200:
        models = response.json().get('models', [])
        print(f"✅ Ollama working locally! Found {len(models)} models:")
        for model in models:
            print(f"  - {model['name']}")
    else:
        print(f"❌ Local test failed: {response.status_code}")
except Exception as e:
    print(f"❌ Local connection error: {e}")

# Use iframe approach for external access
print("\nSetting up external access...")
try:
    # Use iframe approach (recommended by Colab)
    output.serve_kernel_port_as_iframe(11434, height=600)
    print("✅ Ollama is now accessible through Colab's iframe tunneling")
    print("📋 The iframe above should show the Ollama interface")
    
except Exception as e:
    print(f"❌ Iframe setup error: {e}")
    
    # Fallback to window approach
    try:
        print("Trying window approach as fallback...")
        output.serve_kernel_port_as_window(11434)
        print("✅ Ollama accessible through window tunneling")
    except Exception as e2:
        print(f"❌ Window setup error: {e2}")

print("\n" + "="*60)
print("🚀 OLLAMA SERVER STATUS")
print("="*60)
print("✅ Server is running on Colab")
print("📱 Access through the iframe/window above")
print("🔗 For external API access, you may need ngrok or other tunneling")
print("="*60)
