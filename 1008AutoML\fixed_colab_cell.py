# FIXED VERSION - Replace your current cell with this code

import subprocess
import threading
import time
from pyngrok import ngrok

def start_ollama_server():
    """Start Ollama server in background"""
    try:
        subprocess.run(['ollama', 'serve'], check=True)
    except Exception as e:
        print(f"Error starting Ollama server: {e}")

# Start Ollama server in a separate thread
server_thread = threading.Thread(target=start_ollama_server, daemon=True)
server_thread.start()

# Wait a bit for server to start
print("Starting Ollama server...")
time.sleep(5)

# Create ngrok tunnel
try:
    # Kill any existing tunnels
    ngrok.kill()
    
    # Create new tunnel - FIX: Extract URL properly
    tunnel = ngrok.connect(11434, "http")
    public_url = str(tunnel.public_url)  # Convert to string!
    
    print(f"\n🚀 Ollama server is now accessible at: {public_url}")
    print(f"\n📋 Copy this URL and use it as OLLAMA_HOST on your local machine:")
    print(f"export OLLAMA_HOST={public_url}")
    print(f"\nOr in Python: os.environ['OLLAMA_HOST'] = '{public_url}'")
    
    # Store the URL for later use - FIX: Now it's a string
    with open('/content/ollama_url.txt', 'w') as f:
        f.write(public_url)
    
    print(f"\n✅ URL saved to file: {public_url}")
        
except Exception as e:
    print(f"Error creating ngrok tunnel: {e}")
